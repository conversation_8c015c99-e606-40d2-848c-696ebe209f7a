#!/usr/bin/env python3
"""
Test script to verify SharpeRatioRewardScheme integration.
"""

import sys
import os
import logging
import numpy as np
from datetime import datetime

# Add the current directory to the path so we can import from main.py
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the classes we need from main.py
from main import SharpeRatioRewardScheme, FixedPortfolio, TENSORTRADE_CONFIG

def test_sharpe_ratio_reward_scheme():
    """Test the SharpeRatioRewardScheme integration."""
    print("Testing SharpeRatioRewardScheme integration...")
    
    # Set up logging
    logging.basicConfig(level=logging.DEBUG)
    logger = logging.getLogger('test')
    
    try:
        # Create a mock portfolio
        print("Creating FixedPortfolio...")
        portfolio = FixedPortfolio(
            initial_net_worth=100000.0,
            transaction_cost_pct=0.001,
            logger=logger
        )
        
        # Test that the portfolio has the required methods
        print("Testing portfolio methods...")
        
        # Test calculate_net_worth
        net_worth = portfolio.calculate_net_worth()
        print(f"Portfolio net worth: {net_worth}")
        
        # Test initial_net_worth property
        initial_worth = portfolio.initial_net_worth
        print(f"Initial net worth: {initial_worth}")
        
        # Test last_transaction_cost_usd method
        transaction_cost = portfolio.last_transaction_cost_usd()
        print(f"Last transaction cost: {transaction_cost}")
        
        # Test history property
        history = portfolio.history
        print(f"Portfolio history length: {len(history)}")
        
        # Create the SharpeRatioRewardScheme
        print("Creating SharpeRatioRewardScheme...")
        reward_scheme = SharpeRatioRewardScheme(portfolio, logger=logger)
        
        # Test the get_reward method
        print("Testing get_reward method...")
        
        # Test with step 0 (should return simple profit reward)
        reward_0 = reward_scheme.get_reward(step=0)
        print(f"Reward at step 0: {reward_0}")
        
        # Add some mock returns to test Sharpe ratio calculation
        print("Adding mock returns for Sharpe ratio calculation...")
        
        # Simulate some returns
        mock_returns = [0.01, -0.005, 0.02, 0.015, -0.01, 0.008, 0.012, -0.003, 0.018, 0.005]
        reward_scheme.returns_history = mock_returns
        
        # Test with sufficient history (should calculate Sharpe ratio)
        reward_with_history = reward_scheme.get_reward(step=len(mock_returns))
        print(f"Reward with history: {reward_with_history}")
        
        # Test TENSORTRADE_CONFIG access
        print(f"TENSORTRADE_CONFIG window_size: {TENSORTRADE_CONFIG.get('window_size', 'Not found')}")
        
        print("✅ SharpeRatioRewardScheme integration test PASSED!")
        return True
        
    except Exception as e:
        print(f"❌ SharpeRatioRewardScheme integration test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_sharpe_ratio_reward_scheme()
    sys.exit(0 if success else 1)
