2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - setup_logging:597 - Logging to rotating file: logs/rl_portfolio_rebalancing_20250903_210335.log (max 50MB, 5 backups)
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - setup_logging:615 - Memory handler added for critical error buffering
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - setup_logging:623 - Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - setup_logging:624 - Platform: win32
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - setup_logging:625 - Working directory: C:\Users\<USER>\Desktop\Deep 1
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - setup_logging:626 - Process ID: 17560
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - initialize_unicode_safe_logging_early:9828 - [PHASE] Initializing Unicode-safe logging system...
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - initialize_unicode_safe_logging_early:9912 - Unicode logging not available: No module named 'unicode_logging'
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - initialize_unicode_safe_logging_early:9913 -    Using standard logging (this is normal)
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - _initialize_resource_monitoring:14272 - Resource monitoring enabled with psutil
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - _initialize_unicode_safe_logging:14357 - Unicode logging components not available: No module named 'unicode_logging'
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - _initialize_unicode_safe_logging:14358 - Using standard logging (this is normal)
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - __init__:14264 - ComprehensiveLogger initialized with Unicode-safe logging
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - __init__:14265 - Execution started at: 2025-09-03 21:03:35.250346
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - __init__:13442 - SystemErrorHandler initialized
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - __init__:13443 - Error tracking initialized at 2025-09-03 21:03:35.253346
2025-09-03 21:03:35 - rl_portfolio_rebalancing - DEBUG - __init__:13447 - Encoding status stored: False
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - main:9987 - Starting RL Portfolio Rebalancing System in full mode
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15004 - ====================================================================================================
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15005 - RL PORTFOLIO REBALANCING SYSTEM - STARTUP
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15006 - ====================================================================================================
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15007 - Startup time: 2025-09-03 21:03:35
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15008 - Python version: 3.11.9 (tags/v3.11.9:de54cf5, Apr  2 2024, 10:12:12) [MSC v.1938 64 bit (AMD64)]
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15009 - Working directory: C:\Users\<USER>\Desktop\Deep 1
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15010 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15013 - SYSTEM CONFIGURATION
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15014 - --------------------------------------------------
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15016 - DATA_CONFIG:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   etf_symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   risk_free_symbol: ^TNX
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   vix_symbols: ['^VIX', '^VIX3M']
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   start_date: 20XX-01-01
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   frequency: D
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   rebalancing_frequency: M
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   data_source: yfinance
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   vix_feature_enabled: True
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   vix_ratio_bounds: [0.1, 10.0]
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   vix_missing_data_method: forward_fill
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15022 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15016 - TRADING_CONFIG:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   initial_cash: 100000
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   transaction_cost: 0.001
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   slippage_range: [0.0, 0.01]
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   max_allowed_loss: 0.6
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15022 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15016 - TENSORTRADE_CONFIG:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   window_size: 21
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   enable_logger: True
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   action_scheme: portfolio-weights
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   reward_scheme: sharpe-ratio
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15022 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15016 - TRAINING_CONFIG:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   algorithm: PPO
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   total_timesteps: 141000
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   learning_rate: 0.0003
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   batch_size: 64
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   policy_layers: [256, 256]
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   clip_range: 0.2
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   value_function_coeff: 0.5
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   entropy_coeff: 0.01
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15022 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15016 - EVALUATION_CONFIG:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   sharpe_window: 12
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   benchmark_strategy: equal_weight
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   performance_metrics: ['total_return', 'annualized_return', 'volatility', 'sharpe_ratio', 'max_drawdown', 'calmar_ratio']
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15022 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15016 - LOGGING_CONFIG:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   level: DEBUG
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   format: %(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   date_format: %Y-%m-%d %H:%M:%S
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   log_to_file: True
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   log_directory: logs
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   unicode_mode: auto
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   fallback_on_error: True
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   debug_encoding_issues: False
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15022 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15016 - DIRECTORY_CONFIG:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   data: data
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   models: models
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   logs: logs
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   results: results
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   config: config
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15022 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15016 - ENCODING_STATUS:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   unicode_logging_enabled: False
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   encoding_detection_successful: False
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   console_encoding: unknown
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   supports_utf8: False
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   supports_unicode: False
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   recommended_mode: ascii
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   effective_mode: ascii
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   character_mappings_count: 0
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15019 -   initialization_error: Unicode logging components not available: No module named 'unicode_logging'
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15022 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_system_startup:15026 - ====================================================================================================
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14837 -    Timestamp: 2025-09-03 21:03:35
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14838 -    Elapsed: 0:00:00.033126
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14841 -    Details:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      timestamp: 20250903_210335
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      log_file: logs/rl_portfolio_rebalancing_20250903_210335.log
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      directories_created: ['data', 'models', 'logs', 'results', 'config']
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      unicode_logging_enabled: False
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      console_encoding: unknown
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      effective_logging_mode: ascii
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_resource_usage:14814 -   Memory: 6.8GB / 7.8GB (87.2%)
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_resource_usage:14816 -   CPU: 83.2%
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_resource_usage:14817 -   Process Memory: 510.2MB
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_resource_usage:14818 -   Disk: 417.3GB / 465.1GB (89.7%)
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15039 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15041 - PHASE START: SYSTEM CONFIGURATION
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15043 - Description: Printing system information and verifying dependencies
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15044 - Start time: 2025-09-03 21:03:35
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15045 - ================================================================================
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7213 - ================================================================================
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7214 - SYSTEM CONFIGURATION
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7215 - ================================================================================
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7216 - ETF Symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7217 - Risk-free rate symbol: ^TNX
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7218 - Data start date: 20XX-01-01
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7219 - Initial cash: $100,000
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7220 - Transaction cost: 0.1%
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7221 - Rebalancing frequency: D
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7222 - Training timesteps: 141,000
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7223 - Learning rate: 0.0003
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - print_system_info:7224 - ================================================================================
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7141 - Verifying package dependencies...
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7157 - [OK] pandas: 2.2.3
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7157 - [OK] numpy: 1.26.4
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7157 - [OK] yfinance: 0.2.59
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7157 - [OK] matplotlib: unknown
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7157 - [OK] seaborn: 0.13.2
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7166 - [OK] ta: unknown
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7173 - [OK] tensortrade: 1.0.4-dev1
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7181 - [OK] stable-baselines3: 2.6.0
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7188 - [OK] gym: 0.26.2
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - verify_dependencies:7203 - All dependencies verified successfully!
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_end:15072 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_end:15074 - PHASE COMPLETED: SYSTEM CONFIGURATION
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_end:15075 - End time: 2025-09-03 21:03:35
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_end:15076 - Duration: 0:00:00.011997
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_end:15079 - Phase Metrics:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   directories_created: 5
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   dependencies_verified: True
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   logging_configured: True
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_end:15086 - ================================================================================
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14837 -    Timestamp: 2025-09-03 21:03:35
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14838 -    Elapsed: 0:00:00.071316
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14841 -    Details:
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      directories: ['data', 'models', 'logs', 'results', 'config']
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      log_file: logs/rl_portfolio_rebalancing_20250903_210335.log
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_resource_usage:14814 -   Memory: 6.8GB / 7.8GB (87.4%)
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_resource_usage:14816 -   CPU: 79.2%
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_resource_usage:14817 -   Process Memory: 510.2MB
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_resource_usage:14818 -   Disk: 417.3GB / 465.1GB (89.7%)
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15039 - 
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15041 - PHASE START: MODE CONFIGURATION
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15043 - Description: Configuring execution mode
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15044 - Start time: 2025-09-03 21:03:35
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - log_phase_start:15045 - ================================================================================
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - main:10067 - Execution mode: full
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - validate_full_mode:2467 - Validating full mode requirements
2025-09-03 21:03:35 - rl_portfolio_rebalancing - INFO - validate_training_mode:2233 - Validating training mode requirements
2025-09-03 21:03:37 - rl_portfolio_rebalancing - DEBUG - validate_training_mode:2254 - Market data connectivity verified with VT
2025-09-03 21:03:37 - rl_portfolio_rebalancing - DEBUG - validate_training_mode:2272 - TensorTrade library verified
2025-09-03 21:03:37 - rl_portfolio_rebalancing - DEBUG - validate_training_mode:2283 - Stable-baselines3 library verified
2025-09-03 21:03:37 - rl_portfolio_rebalancing - DEBUG - validate_training_mode:2294 - Technical analysis library verified
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - validate_training_mode:2354 - Training mode validation completed successfully
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - validate_full_mode:2495 - Full mode validation completed successfully
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - main:10120 - Mode validation successful for full mode
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_end:15072 - 
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_end:15074 - PHASE COMPLETED: MODE CONFIGURATION
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_end:15075 - End time: 2025-09-03 21:03:37
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_end:15076 - Duration: 0:00:02.264065
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_end:15079 - Phase Metrics:
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   mode: full
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   model_path: None
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   validation_warnings: 0
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_end:15086 - ================================================================================
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_start:15039 - 
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_start:15041 - PHASE START: DATA FETCHING SYSTEM
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_start:15043 - Description: Testing data fetching, preprocessing, and validation
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_start:15044 - Start time: 2025-09-03 21:03:37
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_phase_start:15045 - ================================================================================
2025-09-03 21:03:37 - rl_portfolio_rebalancing - DEBUG - implement_circuit_breaker:13719 - Circuit breaker CLOSED for 'data_fetching' (0 failures < 3 threshold)
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_milestone:14837 -    Timestamp: 2025-09-03 21:03:37
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_milestone:14838 -    Elapsed: 0:00:02.357891
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - __init__:4524 - YFinanceDataFetcher initialized with 3-year window
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - main:10144 - Testing with symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - get_current_window_dates:4549 - Testing trials: using fixed window dates 2022-08-01 to 2025-08-01 (reference_date ignored)
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_milestone:14837 -    Timestamp: 2025-09-03 21:03:37
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_milestone:14838 -    Elapsed: 0:00:02.361892
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_milestone:14841 -    Details:
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      window_start: 2022-08-01
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      window_end: 2025-08-01
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      window_length: 3 years
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - fetch_etf_data:4626 - Fetching ETF data for symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:37 - rl_portfolio_rebalancing - INFO - fetch_etf_data:4627 - Window range: 2022-08-01 to 2025-08-01
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - _validate_etf_data:4755 - ETF data validation completed successfully
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - fetch_etf_data:4688 - Successfully fetched data for 5 symbols
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - fetch_etf_data:4689 - Data shape: (753, 25)
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - fetch_etf_data:4690 - Date range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - fetch_etf_data:4691 - Window coverage: 753/36 months (2091.7%)
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - log_data_quality_report:14954 - ==================================================
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - log_data_quality_report:14964 - Symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - log_data_quality_report:14971 - Missing Data: 0.00%
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - log_data_quality_report:14995 - 
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - main:10173 - [OK] ETF data fetched successfully: (753, 25)
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - fetch_risk_free_rate:4782 - Fetching daily risk-free rate data for symbol: ^TNX
2025-09-03 21:03:40 - rl_portfolio_rebalancing - INFO - fetch_risk_free_rate:4783 - Window range: 2022-08-01 to 2025-08-01
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - _validate_risk_free_data:4964 - Sufficient risk-free rate data: 753 days (target: ~767)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - _validate_risk_free_data:4995 - Risk-free rate data validation completed successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - fetch_risk_free_rate:4835 - Risk-free rate data coverage: 753/1096 days (68.7%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - fetch_risk_free_rate:4839 - Successfully fetched daily risk-free rate data
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - fetch_risk_free_rate:4840 - Data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - fetch_risk_free_rate:4841 - Date range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - fetch_risk_free_rate:4842 - Daily coverage: 753/1096 days (68.7%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10195 - [OK] Risk-free rate data fetched successfully: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14837 -    Timestamp: 2025-09-03 21:03:42
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14838 -    Elapsed: 0:00:07.276899
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14841 -    Details:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      description: Initializing data splitter for mode-specific processing
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:695 - DataSplitter initialized with split ratio: 0.80
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - split_training_evaluation:756 - Data split completed:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - split_training_evaluation:757 -   Total records: 753
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - split_training_evaluation:758 -   Training records: 602 (79.9%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - split_training_evaluation:759 -   Evaluation records: 151 (20.1%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - split_training_evaluation:760 -   Training period: 2022-08-01 to 2024-12-19
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - split_training_evaluation:761 -   Evaluation period: 2024-12-20 to 2025-07-31
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - split_training_evaluation:762 -   Split date: 2024-12-20
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - validate_data_separation:815 - Validating data separation integrity
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - validate_data_separation:895 - Data separation validation passed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - validate_data_separation:896 -   Training period: 2022-08-01 00:00:00 to 2024-12-19 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - validate_data_separation:897 -   Evaluation period: 2024-12-20 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - validate_data_separation:898 -   Data quality score: 1.000
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - validate_data_separation:899 -   Missing data: 0.00%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10237 - [OK] Data splitting validation passed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_data_quality_report:14954 - ==================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_data_quality_report:14971 - Missing Data: 0.00%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_data_quality_report:14995 - 
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - prepare_evaluation_data:936 - Preparing data for full mode
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - prepare_evaluation_data:971 - Full data prepared:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - prepare_evaluation_data:972 -   Records: 753
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - prepare_evaluation_data:973 -   Date range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10252 - [OK] Mode-specific data prepared for full mode: (753, 25)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14837 -    Timestamp: 2025-09-03 21:03:42
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14838 -    Elapsed: 0:00:07.293900
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14841 -    Details:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      mode: full
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      data_shape: (753, 25)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      date_range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - ensure_temporal_alignment:5011 - Ensuring temporal alignment between ETF and risk-free rate data within window
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - ensure_temporal_alignment:5020 - ETF data range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00 (753 months)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - ensure_temporal_alignment:5021 - Risk-free rate data range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00 (753 months)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - ensure_temporal_alignment:5022 - Common date range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - ensure_temporal_alignment:5044 - Aligned ETF data shape: (753, 25)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - ensure_temporal_alignment:5045 - Aligned risk-free rate data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - ensure_temporal_alignment:5046 - Common dates: 753
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - ensure_temporal_alignment:5047 - ETF date coverage: 100.0%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - ensure_temporal_alignment:5048 - Risk-free rate date coverage: 100.0%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10279 - [OK] Data alignment successful: ETF (753, 25), RF (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:5734 - DataPreprocessor initialized
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5747 - Handling missing data using method: forward_fill
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5752 - Missing values before processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5769 - Missing values after processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10310 - [OK] Missing data handling completed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - validate_data_quality:5784 - Validating data quality for VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - validate_data_quality:5823 - Data quality validation passed for VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10315 - [OK] Data quality validation: PASSED
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5897 - Processing OHLCV data for VT (technical indicators disabled, only Close used as features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5900 - Input data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5901 - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5922 - Volume data preserved for future technical indicator implementation
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5926 - Processed data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5927 - Features for RL agent: ['Open', 'High', 'Low', 'Close'] (Volume excluded from features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10327 - [OK] OHLC data processed: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_resource_usage:14814 -   Memory: 7.0GB / 7.8GB (89.7%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_resource_usage:14816 -   CPU: 64.5%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_resource_usage:14817 -   Process Memory: 521.1MB
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_resource_usage:14818 -   Disk: 417.3GB / 465.1GB (89.7%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15072 - 
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15074 - PHASE COMPLETED: DATA FETCHING SYSTEM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15075 - End time: 2025-09-03 21:03:42
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15076 - Duration: 0:00:04.970714
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15079 - Phase Metrics:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   etf_data_available: True
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   risk_free_data_available: True
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   mode_specific_data_available: True
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   execution_mode: full
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   data_preprocessing_completed: True
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   symbols_processed: 5
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15086 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14837 -    Timestamp: 2025-09-03 21:03:42
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14838 -    Elapsed: 0:00:07.331604
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14841 -    Details:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      etf_data_shape: (753, 25)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      risk_free_data_shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10377 - Testing TensorTrade instruments and portfolio setup...
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8461 - Creating fixed TensorTrade instruments and portfolio setup
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8470 - Created USD instrument: FixedInstrument(USD)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8474 - Creating fixed instruments for ETF symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created VT instrument: FixedInstrument(VT)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'id': VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'symbol': VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'name': VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created IEF instrument: FixedInstrument(IEF)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'id': IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'symbol': IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'name': IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created REET instrument: FixedInstrument(REET)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'id': REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'symbol': REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'name': REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created GLD instrument: FixedInstrument(GLD)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'id': GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'symbol': GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'name': GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created COM instrument: FixedInstrument(COM)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'id': COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'symbol': COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'name': COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8489 - Successfully created 6 fixed instruments
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8496 - Created simulated exchange for portfolio
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8499 - Creating fixed TensorTrade portfolio
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8509 - Created USD wallet with $100,000.00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created VT wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created IEF wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created REET wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created GLD wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created COM wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8522 - Successfully created FixedPortfolio with proper TensorTrade integration
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8525 - Portfolio base instrument: USD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8526 - Portfolio wallets: 6
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8531 - Portfolio net worth: $100000.00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8552 - Cash balance: $100000.00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8563 - Portfolio validation successful
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10381 - [OK] TensorTrade instruments created: 6 instruments
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10382 - [OK] Portfolio initialized with 6 wallets
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10384 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10385 - TENSORTRADE INSTRUMENTS AND PORTFOLIO SETUP COMPLETED SUCCESSFULLY
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10386 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10393 - Testing TensorTrade data feeds and streams setup...
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7416 - Creating comprehensive market data streams using TensorTrade Stream API
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7421 - Creating streams for ETF symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:5734 - DataPreprocessor initialized
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7433 - Creating streams for VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5747 - Handling missing data using method: forward_fill
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5752 - Missing values before processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5769 - Missing values after processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5897 - Processing OHLCV data for VT (technical indicators disabled, only Close used as features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5900 - Input data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5901 - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5922 - Volume data preserved for future technical indicator implementation
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5926 - Processed data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5927 - Features for RL agent: ['Open', 'High', 'Low', 'Close'] (Volume excluded from features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_market_data_streams:7468 - Created Close price stream: VT_Close
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7474 - OHLV data available for VT (['Open', 'High', 'Low', 'Volume']) but excluded from RL features
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7495 - Successfully created streams for VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7433 - Creating streams for IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5747 - Handling missing data using method: forward_fill
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5752 - Missing values before processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5769 - Missing values after processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5897 - Processing OHLCV data for IEF (technical indicators disabled, only Close used as features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5900 - Input data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5901 - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5922 - Volume data preserved for future technical indicator implementation
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5926 - Processed data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5927 - Features for RL agent: ['Open', 'High', 'Low', 'Close'] (Volume excluded from features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_market_data_streams:7468 - Created Close price stream: IEF_Close
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7474 - OHLV data available for IEF (['Open', 'High', 'Low', 'Volume']) but excluded from RL features
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7495 - Successfully created streams for IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7433 - Creating streams for REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5747 - Handling missing data using method: forward_fill
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5752 - Missing values before processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5769 - Missing values after processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5897 - Processing OHLCV data for REET (technical indicators disabled, only Close used as features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5900 - Input data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5901 - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5922 - Volume data preserved for future technical indicator implementation
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5926 - Processed data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5927 - Features for RL agent: ['Open', 'High', 'Low', 'Close'] (Volume excluded from features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_market_data_streams:7468 - Created Close price stream: REET_Close
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7474 - OHLV data available for REET (['Open', 'High', 'Low', 'Volume']) but excluded from RL features
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7495 - Successfully created streams for REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7433 - Creating streams for GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5747 - Handling missing data using method: forward_fill
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5752 - Missing values before processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5769 - Missing values after processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5897 - Processing OHLCV data for GLD (technical indicators disabled, only Close used as features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5900 - Input data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5901 - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5922 - Volume data preserved for future technical indicator implementation
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5926 - Processed data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5927 - Features for RL agent: ['Open', 'High', 'Low', 'Close'] (Volume excluded from features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_market_data_streams:7468 - Created Close price stream: GLD_Close
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7474 - OHLV data available for GLD (['Open', 'High', 'Low', 'Volume']) but excluded from RL features
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7495 - Successfully created streams for GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7433 - Creating streams for COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5747 - Handling missing data using method: forward_fill
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5752 - Missing values before processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - handle_missing_data:5769 - Missing values after processing: 0 (0.00%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5897 - Processing OHLCV data for COM (technical indicators disabled, only Close used as features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5900 - Input data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5901 - Input data columns: ['Open', 'High', 'Low', 'Close', 'Volume']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5922 - Volume data preserved for future technical indicator implementation
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5926 - Processed data shape: (753, 5)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_technical_indicators:5927 - Features for RL agent: ['Open', 'High', 'Low', 'Close'] (Volume excluded from features)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_market_data_streams:7468 - Created Close price stream: COM_Close
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7474 - OHLV data available for COM (['Open', 'High', 'Low', 'Volume']) but excluded from RL features
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7495 - Successfully created streams for COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7511 - Market data streams creation completed successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7512 - Total streams created: 5
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7514 -   - price_streams: 5 streams
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7514 -   - volume_streams: 0 streams
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_market_data_streams:7514 -   - technical_streams: 0 streams
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10399 - [OK] Market data streams created successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10400 - [OK] Stream collections: ['price_streams', 'volume_streams', 'technical_streams']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7734 - Setting up DataFeed for observation space
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7739 - Configuring observation window size: 21 days (configurable)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7747 - Added 5 price streams to DataFeed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7752 - Added 0 volume streams to DataFeed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7757 - Added 0 technical indicator streams to DataFeed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7762 - Added 0 risk-free rate streams to DataFeed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7768 - Total streams for DataFeed: 5
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7771 - Adding feature engineering streams for technical indicators
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7802 - Created feature streams for VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7802 - Created feature streams for IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7802 - Created feature streams for REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7802 - Created feature streams for GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7802 - Created feature streams for COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7810 - Added 15 feature engineering streams
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7817 - Creating DataFeed with all market data streams
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7823 - DataFeed created using list approach
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7877 - DataFeed created successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7878 - DataFeed contains 20 streams
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7881 - Configuring DataFeed with observation window: 21 days (configurable)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7894 - Added observation_space to DataFeed: Box(-inf, inf, (420,), float32)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7912 - Added observe method and info attribute to DataFeed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7919 - Compiling DataFeed for data flow preparation
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7924 - DataFeed compilation successful
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7931 - Testing DataFeed with feed.next() to verify data flow
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7943 - DataFeed test iteration 1: observation shape/type: <class 'dict'>
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7947 - Observation keys: ['VT_Close', 'IEF_Close', 'REET_Close', 'GLD_Close', 'COM_Close', 'VT_SMA_10_Stream', 'VT_SMA_20_Stream', 'VT_PriceChange_Stream', 'IEF_SMA_10_Stream', 'IEF_SMA_20_Stream', 'IEF_PriceChange_Stream', 'REET_SMA_10_Stream', 'REET_SMA_20_Stream', 'REET_PriceChange_Stream', 'GLD_SMA_10_Stream', 'GLD_SMA_20_Stream', 'GLD_PriceChange_Stream', 'COM_SMA_10_Stream', 'COM_SMA_20_Stream', 'COM_PriceChange_Stream']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7943 - DataFeed test iteration 2: observation shape/type: <class 'dict'>
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7947 - Observation keys: ['VT_Close', 'IEF_Close', 'REET_Close', 'GLD_Close', 'COM_Close', 'VT_SMA_10_Stream', 'VT_SMA_20_Stream', 'VT_PriceChange_Stream', 'IEF_SMA_10_Stream', 'IEF_SMA_20_Stream', 'IEF_PriceChange_Stream', 'REET_SMA_10_Stream', 'REET_SMA_20_Stream', 'REET_PriceChange_Stream', 'GLD_SMA_10_Stream', 'GLD_SMA_20_Stream', 'GLD_PriceChange_Stream', 'COM_SMA_10_Stream', 'COM_SMA_20_Stream', 'COM_PriceChange_Stream']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7943 - DataFeed test iteration 3: observation shape/type: <class 'dict'>
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7947 - Observation keys: ['VT_Close', 'IEF_Close', 'REET_Close', 'GLD_Close', 'COM_Close', 'VT_SMA_10_Stream', 'VT_SMA_20_Stream', 'VT_PriceChange_Stream', 'IEF_SMA_10_Stream', 'IEF_SMA_20_Stream', 'IEF_PriceChange_Stream', 'REET_SMA_10_Stream', 'REET_SMA_20_Stream', 'REET_PriceChange_Stream', 'GLD_SMA_10_Stream', 'GLD_SMA_20_Stream', 'GLD_PriceChange_Stream', 'COM_SMA_10_Stream', 'COM_SMA_20_Stream', 'COM_PriceChange_Stream']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_Close: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   VT_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   IEF_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   REET_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   GLD_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_SMA_10_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_SMA_20_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7950 -   COM_PriceChange_Stream: shape ()
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7967 - DataFeed data flow verification completed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_datafeed:7976 - DataFeed reset after testing
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7981 - DataFeed setup completed successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7982 - DataFeed configuration summary:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7983 -   - Total streams: 20
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7984 -   - Price streams: 5
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7985 -   - Volume streams: 0
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7986 -   - Technical streams: 0
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7987 -   - Risk-free streams: 0
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7988 -   - Feature streams: 15
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7989 -   - Observation window: 21 days
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_datafeed:7990 -   - Compilation status: Not compiled
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10404 - [OK] DataFeed created successfully for observation space
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10405 - [OK] DataFeed configured with 21 day observation window (configurable)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10407 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10408 - TENSORTRADE DATA FEEDS AND STREAMS SETUP COMPLETED SUCCESSFULLY
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10409 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10418 - Testing TensorTrade exchange with price streams and trading frictions setup...
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7295 - Creating TensorTrade exchange with price streams and trading frictions
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7240 - Configuring execution service with trading frictions
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7247 - Transaction cost: 0.1%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7248 - Slippage range: 0.0% to 1.0%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7264 - Execution service configured successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7265 - Transaction costs and slippage will be applied during order execution
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7268 - Execution service configuration:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7269 -   - Service type: Simulated execution
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7270 -   - Transaction cost: 0.1% per trade
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7271 -   - Slippage modeling: 0.0% to 1.0%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - configure_execution_service_with_frictions:7272 -   - Order execution: Market orders with realistic delays
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7303 - Creating price streams for ETF symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7353 - Created price stream for VT: USD-VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7354 -   - Data points: 753
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7355 -   - Date range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7356 -   - Price range: $74.28 to $132.05
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7353 - Created price stream for IEF: USD-IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7354 -   - Data points: 753
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7355 -   - Date range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7356 -   - Price range: $82.99 to $95.54
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7353 - Created price stream for REET: USD-REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7354 -   - Data points: 753
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7355 -   - Date range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7356 -   - Price range: $18.70 to $26.14
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7353 - Created price stream for GLD: USD-GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7354 -   - Data points: 753
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7355 -   - Date range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7356 -   - Price range: $151.23 to $316.29
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7353 - Created price stream for COM: USD-COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7354 -   - Data points: 753
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7355 -   - Date range: 2022-08-01 00:00:00 to 2025-07-31 00:00:00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7356 -   - Price range: $25.96 to $29.18
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7362 - Successfully created 5 price streams
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7365 - Creating Exchange with execution service including trading frictions
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7370 - Exchange created successfully with trading frictions
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7371 - Exchange name: portfolio_exchange
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7372 - Exchange service: execute_order (simulated with frictions)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7373 - Number of price streams: 5
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7377 - Configured stream names: ['USD-VT', 'USD-IEF', 'USD-REET', 'USD-GLD', 'USD-COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7382 - Trading frictions configuration:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7383 -   - Transaction costs: 0.1% per trade
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7384 -   - Slippage range: 0.0% to 1.0%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_exchange_with_frictions:7385 -   - Impact on order execution: Applied during trade execution
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10424 - [OK] TensorTrade exchange created successfully with trading frictions
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10425 - [OK] Exchange configured with price streams and execution service
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10426 - [OK] Trading frictions configured: transaction costs and slippage
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10428 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10429 - TENSORTRADE EXCHANGE WITH TRADING FRICTIONS SETUP COMPLETED SUCCESSFULLY
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10430 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10439 - Testing complete TensorTrade environment assembly...
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10447 - Using custom PortfolioTradingEnvironment directly (skipping TensorTrade due to compatibility issues)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10453 - Creating custom PortfolioTradingEnvironment...
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9368 - Creating complete PortfolioTradingEnvironment
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9372 - Step 1: Creating TensorTrade instruments and portfolio for wallet setup
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8461 - Creating fixed TensorTrade instruments and portfolio setup
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8470 - Created USD instrument: FixedInstrument(USD)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8474 - Creating fixed instruments for ETF symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created VT instrument: FixedInstrument(VT)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'id': VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'symbol': VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'name': VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created IEF instrument: FixedInstrument(IEF)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'id': IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'symbol': IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'name': IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created REET instrument: FixedInstrument(REET)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'id': REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'symbol': REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'name': REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created GLD instrument: FixedInstrument(GLD)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'id': GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'symbol': GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'name': GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created COM instrument: FixedInstrument(COM)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'id': COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'symbol': COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'name': COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8489 - Successfully created 6 fixed instruments
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8496 - Created simulated exchange for portfolio
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8499 - Creating fixed TensorTrade portfolio
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8509 - Created USD wallet with $100,000.00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created VT wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created IEF wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created REET wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created GLD wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created COM wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8522 - Successfully created FixedPortfolio with proper TensorTrade integration
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8525 - Portfolio base instrument: USD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8526 - Portfolio wallets: 6
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8531 - Portfolio net worth: $100000.00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8552 - Cash balance: $100000.00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8563 - Portfolio validation successful
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9374 - [OK] Created 6 instruments and portfolio with 6 wallets
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9377 - Step 2: Creating custom PortfolioWeightActionScheme with proper wallets
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:6008 - FixedPortfolioWeightActionScheme initialized with 5 ETFs: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9383 - [OK] Created action scheme with 5 ETFs: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:8858 - PortfolioTradingEnvironment initialized with 741 steps
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:8859 - Action space: Box(-1.0, 1.0, (5,), float32)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:8860 - Observation space: Box(-inf, inf, (14,), float32)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9405 - PortfolioTradingEnvironment created successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9406 - Environment ready for training with 741 steps
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9407 - Environment wrapped with GymnasiumWrapper for Stable Baselines3 compatibility
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10455 - [OK] Custom PortfolioTradingEnvironment created successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10458 - Testing custom environment functionality...
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - test_environment_functionality:9512 - Testing environment functionality with gym interface
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - test_environment_functionality:9516 - Test 1: Testing environment.reset() method
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - reset:6833 - SharpeRatioRewardScheme reset called - clearing all internal state
2025-09-03 21:03:42 - rl_portfolio_rebalancing - ERROR - test_environment_functionality:9540 - [FAIL] Environment reset failed: 'SharpeRatioRewardScheme' object has no attribute 'portfolio_returns'
2025-09-03 21:03:42 - rl_portfolio_rebalancing - WARNING - main:10467 - Custom environment functionality testing had issues
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15039 - 
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15041 - PHASE START: MODE EXECUTION
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15043 - Description: Executing full mode
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15044 - Start time: 2025-09-03 21:03:42
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15045 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:695 - DataSplitter initialized with split ratio: 0.80
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10507 - Executing full mode - both training and evaluation
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - execute_full_mode:3272 - Starting full mode execution
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:1154 - OutputManager initialized for mode: full
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - __init__:1155 - Output configuration: OutputConfig(generate_csv=True, generate_json=True, generate_reports=True, output_directory='results', file_prefix='', timestamp_format='%Y%m%d_%H%M%S')
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - execute_full_mode:3278 - Creating full environment
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9368 - Creating complete PortfolioTradingEnvironment
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9372 - Step 1: Creating TensorTrade instruments and portfolio for wallet setup
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8461 - Creating fixed TensorTrade instruments and portfolio setup
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8470 - Created USD instrument: FixedInstrument(USD)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8474 - Creating fixed instruments for ETF symbols: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created VT instrument: FixedInstrument(VT)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'id': VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'symbol': VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - VT instrument has 'name': VT
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created IEF instrument: FixedInstrument(IEF)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'id': IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'symbol': IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - IEF instrument has 'name': IEF
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created REET instrument: FixedInstrument(REET)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'id': REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'symbol': REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - REET instrument has 'name': REET
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created GLD instrument: FixedInstrument(GLD)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'id': GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'symbol': GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - GLD instrument has 'name': GLD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8479 - Created COM instrument: FixedInstrument(COM)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'id': COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'streams': {}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'symbol': COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'precision': 2
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - create_tensortrade_instruments_and_portfolio:8487 - COM instrument has 'name': COM
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8489 - Successfully created 6 fixed instruments
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8496 - Created simulated exchange for portfolio
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8499 - Creating fixed TensorTrade portfolio
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8509 - Created USD wallet with $100,000.00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created VT wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created IEF wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created REET wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created GLD wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8518 - Created COM wallet with 0 shares
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8522 - Successfully created FixedPortfolio with proper TensorTrade integration
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8525 - Portfolio base instrument: USD
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8526 - Portfolio wallets: 6
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8531 - Portfolio net worth: $100000.00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8552 - Cash balance: $100000.00
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_tensortrade_instruments_and_portfolio:8563 - Portfolio validation successful
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9374 - [OK] Created 6 instruments and portfolio with 6 wallets
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9377 - Step 2: Creating custom PortfolioWeightActionScheme with proper wallets
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:6008 - FixedPortfolioWeightActionScheme initialized with 5 ETFs: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9383 - [OK] Created action scheme with 5 ETFs: ['VT', 'IEF', 'REET', 'GLD', 'COM']
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:8858 - PortfolioTradingEnvironment initialized with 741 steps
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:8859 - Action space: Box(-1.0, 1.0, (5,), float32)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - __init__:8860 - Observation space: Box(-inf, inf, (14,), float32)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9405 - PortfolioTradingEnvironment created successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9406 - Environment ready for training with 741 steps
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_complete_portfolio_environment:9407 - Environment wrapped with GymnasiumWrapper for Stable Baselines3 compatibility
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - test_environment_functionality:9512 - Testing environment functionality with gym interface
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - test_environment_functionality:9516 - Test 1: Testing environment.reset() method
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - reset:6833 - SharpeRatioRewardScheme reset called - clearing all internal state
2025-09-03 21:03:42 - rl_portfolio_rebalancing - ERROR - test_environment_functionality:9540 - [FAIL] Environment reset failed: 'SharpeRatioRewardScheme' object has no attribute 'portfolio_returns'
2025-09-03 21:03:42 - rl_portfolio_rebalancing - ERROR - execute_full_mode:3344 - Full mode failed: Environment functionality test failed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - ERROR - main:10533 - [FAIL] Full mode failed: Full mode failed: Environment functionality test failed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15072 - 
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15074 - PHASE FAILED: MODE EXECUTION
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15075 - End time: 2025-09-03 21:03:42
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15076 - Duration: 0:00:00.038277
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15079 - Phase Metrics:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   mode: full
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   execution_time: 0.033273
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   error_message: Full mode failed: Environment functionality test failed
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15086 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - ERROR - main:10541 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - ERROR - main:10542 - FULL MODE EXECUTION FAILED
2025-09-03 21:03:42 - rl_portfolio_rebalancing - ERROR - main:10543 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15039 - 
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15041 - PHASE START: SYSTEM FINALIZATION
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15043 - Description: Creating final summary and cleanup
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15044 - Start time: 2025-09-03 21:03:42
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_start:15045 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_resource_usage:14814 -   Memory: 6.9GB / 7.8GB (88.3%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_resource_usage:14816 -   CPU: 80.2%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_resource_usage:14817 -   Process Memory: 521.5MB
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_resource_usage:14818 -   Disk: 417.3GB / 465.1GB (89.7%)
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_system_execution_summary:15349 - Creating comprehensive system execution summary...
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_system_health_check:13546 -    Health metrics: {'timestamp': '2025-09-03T21:03:42.862439', 'total_errors': 0, 'total_warnings': 0, 'critical_errors': 0, 'recovery_attempts': 0, 'system_status': 'healthy', 'encoding_status': {'unicode_logging_enabled': False, 'console_encoding': 'unknown', 'supports_utf8': False, 'supports_unicode': False, 'effective_mode': 'ascii', 'character_mappings_count': 0, 'initialization_error': "Unicode logging components not available: No module named 'unicode_logging'"}}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_system_health_check:13568 -    Console encoding: unknown
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_system_health_check:13569 -    Effective mode: ascii
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - log_system_health_check:13571 -    Encoding fallback: Unicode logging components not available: No module named 'unicode_logging'
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_system_execution_summary:15532 - Execution summary saved to: results/execution_summary_20250903_210342.txt
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - create_system_execution_summary:15544 - System execution summary created successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10632 - 
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10634 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_system_health_check:13546 -    Health metrics: {'timestamp': '2025-09-03T21:03:42.897439', 'total_errors': 0, 'total_warnings': 0, 'critical_errors': 0, 'recovery_attempts': 0, 'system_status': 'healthy', 'encoding_status': {'unicode_logging_enabled': False, 'console_encoding': 'unknown', 'supports_utf8': False, 'supports_unicode': False, 'effective_mode': 'ascii', 'character_mappings_count': 0, 'initialization_error': "Unicode logging components not available: No module named 'unicode_logging'"}}
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_system_health_check:13568 -    Console encoding: unknown
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_system_health_check:13569 -    Effective mode: ascii
2025-09-03 21:03:42 - rl_portfolio_rebalancing - DEBUG - log_system_health_check:13571 -    Encoding fallback: Unicode logging components not available: No module named 'unicode_logging'
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15072 - 
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15074 - PHASE COMPLETED: SYSTEM FINALIZATION
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15075 - End time: 2025-09-03 21:03:42
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15076 - Duration: 0:00:00.***********-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15079 - Phase Metrics:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   completion_percentage: 80.0
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   final_health_status: healthy
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15081 -   total_execution_time: 0:00:07.***********-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_phase_end:15086 - ================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14837 -    Timestamp: 2025-09-03 21:03:42
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14838 -    Elapsed: 0:00:07.***********-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14841 -    Details:
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      completion_percentage: 80.0%
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      health_status: healthy
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      total_errors: 0
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - log_milestone:14843 -      total_warnings: 0
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10684 - 
========================================================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10685 - FINAL EXECUTION SUMMARY
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10686 - ========================================================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10707 - ========================================================================================================================
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10712 - Creating portfolio visualizations...
2025-09-03 21:03:42 - rl_portfolio_rebalancing - ERROR - create_portfolio_visualizations:16528 - No CSV result files found
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10714 - Portfolio visualizations created successfully
2025-09-03 21:03:42 - rl_portfolio_rebalancing - INFO - main:10742 - System exiting with code: 0
